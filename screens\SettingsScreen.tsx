// ✅ SETTINGS SCREEN CHUẨN UI NHƯ HÌNH ĐÍNH KÈM
import React, { useState, useEffect } from 'react';
import {
  View, Text, StyleSheet, ScrollView,
  Switch, TouchableOpacity, Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { useTts } from '../contexts/TtsContext';
import DeviceInfo from 'react-native-device-info';
import { useNotifications } from '../contexts/NotificationContext';
import PageLayout from '../components/PageLayout';
import axios from 'axios';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Feather from 'react-native-vector-icons/Feather';

export default function SettingsScreen() {
  const { ttsEnabled, setTtsEnabled } = useTts();
  const { notifications } = useNotifications();
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const navigation = useNavigation();

  const fetchProfile = async () => {
    const token = await AsyncStorage.getItem('userToken');
    const userId = await AsyncStorage.getItem('userId');
    if (!token || !userId) return;
    const data = `action=get_profile&user_id=${userId}`;

    try {
      const res = await axios.post('https://api.pay2s.vn/api/v1/user', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`,
        }
      });
      if (res.data.status) setProfile(res.data.message);
    } catch (err) {}
  };

  useEffect(() => {
    fetchProfile();
    AsyncStorage.getItem('biometric_enabled').then(v => setBiometricEnabled(v === 'true'));
  }, []);

  const toggleTTS = async () => {
    const newVal = !ttsEnabled;
    setTtsEnabled(newVal);
    await AsyncStorage.setItem('tts_enabled', newVal.toString());
  };

  const toggleBiometric = async () => {
    const newVal = !biometricEnabled;
    setBiometricEnabled(newVal);
    await AsyncStorage.setItem('biometric_enabled', newVal.toString());
  };

  const logout = async () => {
    await AsyncStorage.clear();
    Alert.alert('Đăng xuất thành công');
    navigation.reset({ index: 0, routes: [{ name: 'Login' }] });
  };

  return (
    <PageLayout>
      <ScrollView contentContainerStyle={styles.container}>
        {profile && (
          <View style={styles.card}>
            <View style={styles.headerRow}>
              <View style={styles.circle}><Text style={styles.circleText}>{profile.firstname.charAt(0)}{profile.lastname.charAt(0)}</Text></View>
              <View style={{ marginLeft: 12 }}>
                <Text style={styles.name}>{profile.firstname} {profile.lastname}</Text>
                <Text style={styles.username}>{profile.username}</Text>
              </View>
            </View>

            <View style={styles.infoRow}><FontAwesome name="envelope" style={styles.icon} />
              <Text style={styles.infoText}>{profile.email}</Text></View>

            <View style={styles.infoRow}><FontAwesome name="archive" style={styles.icon} />
              <Text style={styles.infoText}>Gói {profile.current_plan || 'cá nhân'}
                {'\n'}<Text style={{ color: '#aaa' }}>({profile.billingcycle})</Text></Text></View>

            <View style={styles.infoRow}><FontAwesome name="calendar" style={styles.icon} />
              <Text style={styles.infoText}>Sử dụng đến {'\n'}<Text style={{ color: '#aaa' }}>{profile.expire_date?.split('-').reverse().join('/')}</Text></Text></View>
          </View>
        )}

        <View style={styles.settingBox}>
          <Text style={styles.sectionTitle}>Cài đặt thông báo</Text>
          <View style={styles.settingItem}>
            <View>
              <Text style={styles.settingTitle}>Đọc giao dịch bằng giọng nói</Text>
              <Text style={styles.settingDesc}>Tự động đọc số tiền khi có giao dịch mới</Text>
            </View>
            <Switch value={ttsEnabled} onValueChange={toggleTTS} trackColor={{ true: '#00CBA9', false: '#ccc' }} />
          </View>
        </View>

        <View style={styles.settingBox}>
          <View style={styles.settingItem}>
            <View>
              <Text style={styles.settingTitle}>Đăng nhập bằng vân tay</Text>
              <Text style={styles.settingDesc}>Cho phép xác thực vân tay khi mở ứng dụng</Text>
            </View>
            <Switch value={biometricEnabled} onValueChange={toggleBiometric} trackColor={{ true: '#00CBA9', false: '#ccc' }} />
          </View>
        </View>

        <TouchableOpacity style={styles.legalBox} onPress={() => navigation.navigate('LegalInfo')}>
          <Text style={styles.legalText}>Thông tin pháp lý</Text>
          <FontAwesome name="angle-right" size={20} color="#888" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.logoutBtn} onPress={logout}>
          <Text style={styles.logoutText}>Đăng xuất</Text>
        </TouchableOpacity>

        <Text style={styles.version}>Phiên bản {DeviceInfo.getVersion()} (build {DeviceInfo.getBuildNumber()})</Text>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: { padding: 20 },
  card: {
    backgroundColor: '#fff', borderRadius: 16, padding: 20,
    marginBottom: 20, shadowColor: '#000', shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 }, shadowRadius: 5, elevation: 3
  },
  headerRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 20 },
  circle: { width: 60, height: 60, borderRadius: 30, backgroundColor: '#308a5a', justifyContent: 'center', alignItems: 'center' },
  circleText: { color: '#fff', fontSize: 20, fontWeight: 'bold' },
  name: { fontSize: 18, fontWeight: 'bold', color: '#222' },
  username: { color: '#888', marginTop: 4 },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  icon: { marginRight: 10, fontSize: 18, color: '#308a5a' },
  infoText: { fontSize: 15, color: '#333' },
  settingBox: { backgroundColor: '#fff', borderRadius: 12, padding: 16, marginBottom: 16 },
  settingItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  settingTitle: { fontWeight: '600', fontSize: 15, color: '#222' },
  settingDesc: { fontSize: 13, color: '#888', marginTop: 2 },
  sectionTitle: { fontWeight: 'bold', fontSize: 16, marginBottom: 12, color: '#2c3e50' },
  legalBox: {
    backgroundColor: '#fff', borderRadius: 12, padding: 16, marginBottom: 20,
    flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'
  },
  legalText: { fontSize: 15, color: '#222' },
  logoutBtn: {
    backgroundColor: '#e53935', borderRadius: 12, paddingVertical: 14,
    alignItems: 'center', marginBottom: 20
  },
  logoutText: { color: '#fff', fontWeight: 'bold', fontSize: 16 },
  version: { fontSize: 12, color: '#aaa', textAlign: 'center' }
});
