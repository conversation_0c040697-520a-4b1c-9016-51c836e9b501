import Tts from 'react-native-tts';

export const speakTransaction = (amount: number, account: string) => {
  Tts.setDefaultLanguage('vi-VN');
  Tts.setDefaultRate(0.45);
  Tts.setDucking(true); // hạ volume nhạc nền

  const spokenAccount = account.split('').join(' ');
  const spokenAmount = amount.toLocaleString('vi-VN');

  const message = `Bạn vừa nhận được ${spokenAmount} đồng vào tài khoản ${spokenAccount}`;
  Tts.speak(message);
};
