import React, { useEffect, useState } from 'react';
import {
  View, Text, ActivityIndicator, Alert,
  StyleSheet, ScrollView, TouchableOpacity, Share
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import PageLayout from '../components/PageLayout';
import moment from 'moment';

export default function OrderDetailScreen({ route }) {
  const { orderId } = route.params;
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const translateStatus = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return { label: '✅ Đã thanh toán', color: '#27ae60' };
      case 'pending':
        return { label: '⏳ Đang chờ', color: '#f39c12' };
      case 'canceled':
        return { label: '❌ Đã huỷ', color: '#e74c3c' };
      case 'failed':
        return { label: '❌ Thất bại', color: '#c0392b' };
      default:
        return { label: status, color: '#7f8c8d' };
    }
  };

  useEffect(() => {
    const fetchOrderDetail = async () => {
      const token = await AsyncStorage.getItem('userToken');
      try {
        const res = await axios.get(
          `https://payment.pay2s.vn/api/v1/orders/detail?requestId=${orderId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        if (res.data.success) {
          setOrder(res.data.data);
        } else {
          Alert.alert('Lỗi', res.data.message || 'Không tìm thấy đơn hàng.');
        }
      } catch (err) {
        console.log('❌ Lỗi:', err);
        Alert.alert('Lỗi', 'Không thể tải chi tiết đơn hàng.');
      } finally {
        setLoading(false);
      }
    };
    fetchOrderDetail();
  }, []);

  if (loading) {
    return (
      <PageLayout>
        <ActivityIndicator size="large" style={{ marginTop: 40 }} />
      </PageLayout>
    );
  }

  if (!order) {
    return (
      <PageLayout>
        <Text style={{ textAlign: 'center', marginTop: 40 }}>Không có dữ liệu</Text>
      </PageLayout>
    );
  }

  const statusData = translateStatus(order.status);
const handleShare = () => {
  if (!order) return;

  const message = `
🔔 Thông tin hoá đơn:
• Mã đơn: ${order.order_id}
• Số tiền: ${parseInt(order.amount).toLocaleString()} ₫
• Trạng thái: ${translateStatus(order.status).label}
• Ngày tạo: ${moment(order.created_at).format('DD/MM/YYYY HH:mm')}
  `;

  Share.share({ message });
};
  return (
    <PageLayout>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.card}>
          <Text style={styles.title}>Chi tiết hoá đơn</Text>

          <View style={styles.item}>
            <Text style={styles.label}>Mã đơn hàng:</Text>
            <Text style={styles.value, styles.mono}>{order.order_id}</Text>
          </View>

          <View style={styles.item}>
            <Text style={styles.label}>Số tiền:</Text>
            <Text style={[styles.value, styles.amount]}>
              {parseInt(order.amount).toLocaleString()} ₫
            </Text>
          </View>

          <View style={styles.item}>
            <Text style={styles.label}>Số hoá đơn:</Text>
            <Text style={styles.value, styles.mono}>{order.invoice_number || '—'}</Text>
          </View>

          <View style={styles.item}>
            <Text style={styles.label}>Trạng thái:</Text>
            <Text style={[styles.value, { color: statusData.color, fontWeight: 'bold' }]}>
              {statusData.label}
            </Text>
          </View>

          <View style={styles.item}>
            <Text style={styles.label}>Ngày tạo:</Text>
           <Text style={styles.value}>
             {moment(order.created_at).format('DD/MM/YYYY HH:mm')}
           </Text>
          </View>
          <TouchableOpacity style={styles.shareBtn} onPress={handleShare}>
            <Text style={styles.shareText}>📤 Chia sẻ hoá đơn</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
    shareBtn: {
      marginTop: 16,
      backgroundColor: '#308a5a',
      padding: 12,
      borderRadius: 8,
      alignItems: 'center'
    },
    shareText: {
      color: '#fff',
      fontWeight: 'bold'
    },
  container: {
    padding: 20,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#308a5a',
    marginBottom: 16,
    textAlign: 'center',
    borderBottomWidth: 1,
    borderColor: '#ddd',
    paddingBottom: 10
  },
  item: {
    marginBottom: 14,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0'
  },
  label: {
    color: '#666',
    fontSize: 13,
    fontWeight: '600'
  },
  value: {
    fontSize: 15,
    color: '#2c3e50',
    marginTop: 4
  },
  amount: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#e67e22'
  },
  mono: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace'
  }
});

