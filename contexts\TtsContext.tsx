import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface TtsContextType {
  ttsEnabled: boolean;
  setTtsEnabled: (value: boolean) => void;
}

const TtsContext = createContext<TtsContextType>({
  ttsEnabled: true,
  setTtsEnabled: () => {}
});

export const TtsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [ttsEnabled, setTtsEnabledState] = useState(true);

  useEffect(() => {
    AsyncStorage.getItem('tts_enabled').then(value => {
      setTtsEnabledState(value !== 'false');
    });
  }, []);

  const setTtsEnabled = (value: boolean) => {
    setTtsEnabledState(value);
    AsyncStorage.setItem('tts_enabled', value.toString());
  };

  return (
    <TtsContext.Provider value={{ ttsEnabled, setTtsEnabled }}>
      {children}
    </TtsContext.Provider>
  );
};

export const useTts = () => useContext(TtsContext);
