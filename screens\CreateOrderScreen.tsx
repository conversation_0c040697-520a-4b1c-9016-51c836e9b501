// CreateOrderScreen.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

export default function CreateOrderScreen() {
  const navigation = useNavigation();
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCreateOrder = async () => {
    if (!amount) return Alert.alert('Lỗi', 'Vui lòng nhập số tiền');
    setLoading(true);
    try {
      const res = await fetch('https://payment.pay2s.vn/api/v1/orders/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partnerCode: 'PAY2S7ZMLKFJFURCGPEM',
          amount: parseInt(amount),
          orderInfo: 'Thanh toán từ App',
          requestId: 'ORDER' + Date.now(),
          bankIds: 952 // ID ngân hàng đã liên kết của user
        })
      });
      const data = await res.json();
      if (data.resultCode === 0) {
        navigation.navigate('PaymentScreen', {
          qrcodeUrl: data.qrcode,
          amount: data.amount,
          bankInfo: data.bank_list[0]
        });
      } else {
        Alert.alert('Lỗi', data.message);
      }
    } catch (err) {
      Alert.alert('Lỗi', 'Không thể kết nối máy chủ');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Nhập số tiền cần thanh toán:</Text>
      <TextInput
        keyboardType="numeric"
        value={amount}
        onChangeText={setAmount}
        placeholder="Nhập số tiền...",
          style={[
            styles.searchBox,
            {
              color: '#2c3e50',           // ✅ màu chữ
              backgroundColor: '#fff',    // ✅ nên có để tránh bị đè bởi dark mode
            },
          ]}
      />
      <TouchableOpacity style={styles.button} onPress={handleCreateOrder} disabled={loading}>
        {loading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Tạo đơn hàng</Text>}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, justifyContent: 'center', backgroundColor: '#fff' },
  label: { fontSize: 16, marginBottom: 10 },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
    marginBottom: 20,
    fontSize: 16,
    color: '#2c3e50'
  },
  button: {
    backgroundColor: '#308a5a',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center'
  },
  buttonText: { color: '#fff', fontWeight: 'bold', fontSize: 16 }
});
