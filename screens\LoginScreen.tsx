import React, { useState, useEffect } from 'react';
import {
  View, Text, TextInput, StyleSheet, ScrollView, Image,
  TouchableOpacity, Dimensions, ImageBackground, KeyboardAvoidingView,
  Platform, Keyboard
} from 'react-native';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import socket from '../socket';
import Toast from 'react-native-toast-message';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import ReactNativeBiometrics from 'react-native-biometrics';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const rnBiometrics = new ReactNativeBiometrics();

const { width } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [secure, setSecure] = useState(true);
  const [rememberMe, setRememberMe] = useState(true);
  const [biometryType, setBiometryType] = useState(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const checkRemembered = async () => {
      const savedUsername = await AsyncStorage.getItem('rememberedUsername');
      const savedPassword = await AsyncStorage.getItem('rememberedPassword');
      const biometricEnabled = await AsyncStorage.getItem('biometric_enabled');

      const { available, biometryType } = await rnBiometrics.isSensorAvailable();
      if (available) setBiometryType(biometryType);

      if (savedUsername && savedPassword) {
        setUsername(savedUsername);
        setPassword(savedPassword);
        setRememberMe(true);

        if (biometricEnabled === 'true' && available) {
          const { success } = await rnBiometrics.simplePrompt({ promptMessage: 'Xác thực để đăng nhập' });
          if (success) handleLogin(savedUsername, savedPassword);
        }
      }
    };

    checkRemembered();

    const show = Keyboard.addListener('keyboardDidShow', () => setKeyboardOpen(true));
    const hide = Keyboard.addListener('keyboardDidHide', () => setKeyboardOpen(false));
    return () => { show.remove(); hide.remove(); };
  }, []);

  const handleBiometricLogin = async () => {
    const biometricSetting = await AsyncStorage.getItem('biometric_enabled');
    if (biometricSetting !== 'true') return Toast.show({ type: 'info', text1: 'Chưa bật đăng nhập vân tay' });

    const { available } = await rnBiometrics.isSensorAvailable();
    if (!available) return Toast.show({ type: 'error', text1: 'Thiết bị không hỗ trợ vân tay' });

    const { success } = await rnBiometrics.simplePrompt({ promptMessage: 'Xác thực vân tay để đăng nhập' });
    if (success) {
      const savedUsername = await AsyncStorage.getItem('rememberedUsername');
      const savedPassword = await AsyncStorage.getItem('rememberedPassword');
      if (savedUsername && savedPassword) handleLogin(savedUsername, savedPassword);
    }
  };

  const handleLogin = async (loginUser = username, loginPass = password) => {
    if (!loginUser || !loginPass) return Toast.show({ type: 'error', text1: 'Thiếu thông tin đăng nhập' });

    try {
      const data = `action=login&username=${encodeURIComponent(loginUser)}&password=${encodeURIComponent(loginPass)}`;
      const res = await axios.post('https://api.pay2s.vn/api/v1/auth', data, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      if (res.data.status) {
        await AsyncStorage.setItem('userToken', res.data.token);
        await AsyncStorage.setItem('userId', res.data.user_id.toString());
        if (rememberMe) {
          await AsyncStorage.setItem('rememberedUsername', loginUser);
          await AsyncStorage.setItem('rememberedPassword', loginPass);
        }

        socket.connect();
        socket.emit('join', res.data.user_id.toString());

        const fcmToken = await messaging().getToken();
        const fcmData = `action=user_fcm_tokens&token=${encodeURIComponent(fcmToken)}&user_id=${res.data.user_id}`;
        await axios.post('https://api.pay2s.vn/api/v1/user', fcmData, {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded', Authorization: `Bearer ${res.data.token}` }
        });

        navigation.navigate('Home');
      } else {
        Toast.show({ type: 'error', text1: 'Sai thông tin đăng nhập', text2: res.data.message });
      }
    } catch (err) {
      Toast.show({ type: 'error', text1: 'Không thể kết nối máy chủ' });
    }
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{ flex: 1 }}>
      <ImageBackground source={{ uri: 'https://my.pay2s.vn/assets/media/photos/<EMAIL>' }} style={styles.background}>
        <View style={styles.overlay} />
        <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
          <View style={styles.form}>
            {!keyboardOpen && <Image source={{ uri: 'https://docs.pay2s.vn/assets/logo.png' }} style={styles.logo} />}
            <Text style={styles.title}>Đăng nhập hệ thống</Text>
            <View style={styles.inputGroup}>
              <FontAwesome name="user" size={18} color="#888" style={styles.icon} />
              <TextInput
                placeholder="Tên đăng nhập"
                value={username}
                onChangeText={setUsername}
                style={styles.input}
                placeholderTextColor="#aaa"
              />
            </View>
            <View style={styles.inputGroup}>
              <FontAwesome name="lock" size={18} color="#888" style={styles.icon} />
              <TextInput
                placeholder="Mật khẩu"
                value={password}
                onChangeText={setPassword}
                style={styles.input}
                secureTextEntry={secure}
                placeholderTextColor="#aaa"
              />
              <TouchableOpacity onPress={() => setSecure(!secure)}>
                <FontAwesome name={secure ? 'eye-slash' : 'eye'} size={18} color="#888" />
              </TouchableOpacity>
            </View>
         {/*   <View style={styles.rememberRow}>
              <Text style={styles.rememberText}>Ghi nhớ tài khoản</Text>
              <TouchableOpacity onPress={() => setRememberMe(!rememberMe)}>
                <FontAwesome name={rememberMe ? 'check-square' : 'square-o'} size={18} color="#308a5a" />
              </TouchableOpacity>
            </View> */}
            <TouchableOpacity style={styles.button} onPress={() => handleLogin()}>
              <Text style={styles.buttonText}>ĐĂNG NHẬP</Text>
            </TouchableOpacity>
          {biometryType && (
            <>
             <TouchableOpacity onPress={handleBiometricLogin} style={styles.fingerprintIcon}>
               <MaterialIcons name="fingerprint" size={42} color="#308a5a" />
             </TouchableOpacity>
              <Text style={styles.fingerprintText}>
                Đăng nhập bằng {biometryType === 'FaceID' ? 'khuôn mặt' : 'vân tay'}
              </Text>
            </>
          )}
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.registerText}>
                Bạn chưa có tài khoản? <Text style={{ color: '#308a5a', fontWeight: 'bold' }}>Đăng ký</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
   fingerprintIcon: {
     marginTop: 16,
     marginBottom: 4,
     alignItems: 'center',
     justifyContent: 'center',
   },

   fingerprintText: {
     color: '#308a5a',
     fontSize: 14,
     fontWeight: '500',
     textAlign: 'center',
   },
    registerText: {
      marginTop: 8,
      fontSize: 14,
      color: '#555',
    },
  background: { flex: 1 },
  overlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.7)' },
  container: { flexGrow: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  form: {
    backgroundColor: '#fff', borderRadius: 14, padding: 24, alignItems: 'center', width: width * 0.85,
    shadowColor: '#000', shadowOpacity: 0.2, shadowRadius: 10, shadowOffset: { width: 0, height: 3 }, elevation: 5,
  },
  logo: { width: width * 0.4, height: width * 0.4, marginBottom: 15, resizeMode: 'contain' },
  title: { fontSize: 18, fontWeight: 'bold', marginBottom: 20, color: '#2c3e50' },
  inputGroup: {
    flexDirection: 'row', alignItems: 'center', borderWidth: 1, borderColor: '#ccc', borderRadius: 6,
    marginBottom: 15, paddingHorizontal: 10, width: '100%', backgroundColor: '#fff'
  },
  icon: { marginRight: 10 },
  input: { flex: 1, height: 44, color: '#333' },
  rememberRow: {
    flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', marginBottom: 20,
  },
  rememberText: { color: '#555' },
  button: {
    backgroundColor: '#308a5a', paddingVertical: 12, borderRadius: 6,
    width: '100%', alignItems: 'center', marginBottom: 15,
  },
  buttonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
  biometricText: { color: '#308a5a', fontWeight: 'bold', fontSize: 15 },
});

export default LoginScreen;