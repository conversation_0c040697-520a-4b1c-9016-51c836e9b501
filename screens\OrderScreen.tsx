import React, { useEffect, useState } from 'react';
import {
  View, Text, StyleSheet, TextInput, TouchableOpacity,
  Alert, ScrollView, Modal
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import PageLayout from '../components/PageLayout';
import { useNavigation } from '@react-navigation/native';

export default function OrderScreen() {
  const navigation = useNavigation();
  const [amount, setAmount] = useState('');
  const [selectedBank, setSelectedBank] = useState<any>(null);
  const [bankList, setBankList] = useState([]);
  const [filteredBanks, setFilteredBanks] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBankModal, setShowBankModal] = useState(false);
  const [orderHistory, setOrderHistory] = useState([]);

  const formatCurrency = (value: string) => {
    const onlyNumbers = value.replace(/[^\d]/g, '');
    return onlyNumbers.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  const fetchBankList = async () => {
    const token = await AsyncStorage.getItem('userToken');
    const userId = await AsyncStorage.getItem('userId');
    const data = `action=bank_account&user_id=${userId}`;

    try {
      const res = await axios.post('https://api.pay2s.vn/api/v1/bank', data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`,
        },
      });
      if (res.data.status && Array.isArray(res.data.banks)) {
        setBankList(res.data.banks);
        setFilteredBanks(res.data.banks);
      }
    } catch (err) {
      Alert.alert('Lỗi', 'Không lấy được danh sách ngân hàng.');
    }
  };

  const fetchOrderHistory = async () => {
    const token = await AsyncStorage.getItem('userToken');

    try {
      const res = await axios.post(
        'https://payment.pay2s.vn/api/v1/orders/history',
        { requestId: '' }, // để trống nếu muốn lấy toàn bộ đơn
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          }
        }
      );
      if (res.data.success && Array.isArray(res.data.data)) {
        setOrderHistory(res.data.data);
      }
    } catch (err: any) {
      console.log('❌ Lỗi fetch history:', err.response?.data || err.message);
    }
  };


  useEffect(() => {
    fetchBankList();
    fetchOrderHistory();
  }, []);
    const translateStatus = (status: string) => {
      switch (status.toLowerCase()) {
        case 'completed':
          return '✅ Đã thanh toán';
        case 'pending':
          return '⏳ Đang chờ thanh toán';
        case 'canceled':
          return '❌ Đã huỷ';
          case 'cancelled':
                    return '❌ Đã huỷ';
        case 'failed':
          return '❌ Thất bại';
        default:
          return status;
      }
    };
  const createOrder = async () => {
    if (!amount || !selectedBank) {
      Alert.alert('Thiếu thông tin', 'Vui lòng nhập số tiền và chọn ngân hàng.');
      return;
    }

    const partnerCode = await AsyncStorage.getItem('partnerCode');
    const token = await AsyncStorage.getItem('userToken');
    const requestId = 'ORDER' + Date.now();
    const amountNumber = amount.replace(/\./g, '');
    const body = {
      partnerCode,
      amount: parseFloat(amountNumber),
      orderInfo: 'Thanh toán đơn hàng',
      requestId,
      bankIds: selectedBank.id
    };

    try {
      const res = await axios.post(
        'https://payment.pay2s.vn/api/v1/orders/create',
        body,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (res.data.resultCode === 0) {
        navigation.navigate('Payment', {
          name: res.data.name,
          orderId: res.data.requestId,
          amount: res.data.amount,
          qrcode: res.data.qrcode,
          payUrl: res.data.payUrl,
          bank_list: res.data.bank_list
        });
      } else {
        Alert.alert('Thất bại', res.data.message || 'Không tạo đơn được.');
      }
    } catch (err: any) {
      console.log('❌ Lỗi gửi:', err.response?.data || err.message);
      Alert.alert('Lỗi', 'Không gửi được yêu cầu. Kiểm tra kết nối hoặc định dạng.');
    }
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
    const filtered = bankList.filter((bank: any) =>
      bank.bankName.toLowerCase().includes(text.toLowerCase()) ||
      bank.accountNumber?.includes(text) ||
      bank.vaNumber?.includes(text)
    );
    setFilteredBanks(filtered);
  };

  return (
    <PageLayout>
      <ScrollView style={{ padding: 24 }}>
        <Text style={styles.title}>Tạo hóa đơn thanh toán</Text>

        <Text style={styles.label}>Số tiền</Text>
        <TextInput
          style={[styles.input, {
                                      color: '#2c3e50',           // ✅ màu chữ
                                      backgroundColor: '#fff',    // ✅ nên có để tránh bị đè bởi dark mode
                                    }]}
          keyboardType="numeric"
          placeholder="Nhập số tiền (VND)"
          value={amount}
          onChangeText={(text) => setAmount(formatCurrency(text))}
        />

        <Text style={styles.label}>Ngân hàng nhận</Text>
        <TouchableOpacity style={styles.bankSelector} onPress={() => setShowBankModal(true)}>
          <Text style={{ color: selectedBank ? '#000' : '#aaa' }}>
            {selectedBank ? `${selectedBank.bankName} - ${selectedBank.accountNumber || selectedBank.vaNumber}` : 'Chọn ngân hàng'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.submitBtn} onPress={createOrder}>
          <Text style={styles.submitText}>Tạo hóa đơn</Text>
        </TouchableOpacity>

        <View style={styles.orderList}>
          {orderHistory.map((order: any) => (
            <TouchableOpacity
              key={order.id}
              style={styles.orderCard}
              onPress={() => navigation.navigate('OrderDetail', { orderId: order.order_id })}
            >
              <View style={styles.orderRow}>
                <Text style={styles.orderLabel}>Mã đơn:</Text>
                <Text style={styles.orderValue}>{order.order_id}</Text>
              </View>
              <View style={styles.orderRow}>
                <Text style={styles.orderLabel}>Số tiền:</Text>
                <Text style={[styles.orderValue, { color: '#e67e22', fontWeight: 'bold' }]}>
                  {Number(order.amount).toLocaleString()} ₫
                </Text>
              </View>
              <View style={styles.orderRow}>
                <Text style={styles.orderLabel}>Trạng thái:</Text>
                <Text style={[
                  styles.orderValue,
                  {
                    color:
                      order.status === 'completed'
                        ? '#27ae60'
                        : order.status === 'pending'
                        ? '#f39c12'
                        : '#e74c3c'
                  }
                ]}>
                  {translateStatus(order.status)}
                </Text>
              </View>
              <View style={styles.orderRow}>
                <Text style={styles.orderLabel}>Ngày tạo:</Text>
                <Text style={styles.orderValue}>{order.created_at}</Text>
              </View>
             </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Modal chọn ngân hàng */}
      <Modal visible={showBankModal} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Chọn ngân hàng</Text>

            <TextInput
              placeholder="Tìm theo tên hoặc STK"
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={handleSearch}
            />

            <ScrollView>
              {filteredBanks.map(bank => (
                <TouchableOpacity
                  key={bank.id}
                  style={styles.bankOption}
                  onPress={() => {
                    setSelectedBank(bank);
                    setShowBankModal(false);
                    setSearchTerm('');
                    setFilteredBanks(bankList); // reset filter
                  }}
                >
                  <Text style={styles.bankName}>{bank.bankName}</Text>
                  <Text style={styles.bankAccount}>STK: {bank.accountNumber || bank.vaNumber}</Text>
                </TouchableOpacity>
              ))}
              {filteredBanks.length === 0 && (
                <Text style={{ textAlign: 'center', padding: 20, color: '#999' }}>
                  Không tìm thấy ngân hàng phù hợp.
                </Text>
              )}
            </ScrollView>

            <TouchableOpacity onPress={() => {
              setShowBankModal(false);
              setSearchTerm('');
              setFilteredBanks(bankList);
            }}>
              <Text style={styles.closeModal}>Đóng</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
    orderList: {
      marginTop: 20,
      marginBottom: 30,
    },
    orderCard: {
      backgroundColor: '#fff',
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
      borderLeftWidth: 5,
      borderLeftColor: '#308a5a'
    },
    orderRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 6
    },
    orderLabel: {
      fontWeight: '600',
      color: '#555',
      fontSize: 14
    },
    orderValue: {
      fontSize: 14,
      color: '#333',
      flexShrink: 1,
      textAlign: 'right'
    },
  title: {
    fontSize: 20, fontWeight: 'bold', color: '#308a5a',
    marginBottom: 20, textAlign: 'center'
  },
  label: {
    marginBottom: 6, fontWeight: '600', color: '#444'
  },
  input: {
    borderWidth: 1, borderColor: '#ccc', padding: 12,
    borderRadius: 8, marginBottom: 16, fontSize: 16
  },
  bankSelector: {
    padding: 14, borderWidth: 1, borderColor: '#ccc',
    borderRadius: 8, marginBottom: 24, backgroundColor: '#f9f9f9'
  },
  submitBtn: {
    backgroundColor: '#308a5a', padding: 14,
    alignItems: 'center', borderRadius: 8
  },
  submitText: {
    color: '#fff', fontWeight: 'bold', fontSize: 16
  },
  modalOverlay: {
    flex: 1, backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center', padding: 20
  },
  modalContent: {
    backgroundColor: '#fff', borderRadius: 10,
    padding: 20, maxHeight: '80%'
  },
  modalTitle: {
    fontSize: 18, fontWeight: 'bold', marginBottom: 12,
    color: '#308a5a', textAlign: 'center'
  },
  searchInput: {
    borderWidth: 1, borderColor: '#ddd', padding: 10,
    borderRadius: 8, marginBottom: 12, fontSize: 14
  },
  bankOption: {
    paddingVertical: 10, borderBottomWidth: 1, borderBottomColor: '#eee'
  },
  bankName: {
    fontWeight: 'bold', color: '#2c3e50'
  },
  bankAccount: {
    color: '#888'
  },
  closeModal: {
    textAlign: 'center', marginTop: 14,
    color: '#e74c3c', fontWeight: 'bold'
  }
});
