import React, { useState } from 'react';
import {
  View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView,
  Dimensions, Image, ImageBackground, KeyboardAvoidingView, Platform
} from 'react-native';
import axios from 'axios';
import Toast from 'react-native-toast-message';
import FontAwesome from 'react-native-vector-icons/FontAwesome';

const { width } = Dimensions.get('window');

const RegisterScreen = ({ navigation }) => {
  const [firstname, setFirstname] = useState('');
  const [lastname, setLastname] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [repassword, setRepassword] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');

  const handleRegister = async () => {
    if (!firstname || !lastname || !username || !password || !repassword || !email || !phone) {
      Toast.show({ type: 'error', text1: 'Thiếu thông tin', text2: '<PERSON><PERSON> lòng nhập đầy đủ các trường.' });
      return;
    }
    if (password !== repassword) {
      Toast.show({ type: 'error', text1: 'Mật khẩu không khớp' });
      return;
    }

    try {
      const data = `action=register&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&repassword=${encodeURIComponent(repassword)}&firstname=${encodeURIComponent(firstname)}&lastname=${encodeURIComponent(lastname)}&email=${encodeURIComponent(email)}&phone=${encodeURIComponent(phone)}`;
      const res = await axios.post('https://api.pay2s.vn/api/v1/auth', data, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      if (res.data.status) {
        Toast.show({ type: 'success', text1: 'Tạo tài khoản thành công' });
        navigation.navigate('Login');
      } else {
        Toast.show({ type: 'error', text1: 'Lỗi', text2: res.data.message });
      }
    } catch (err) {
      Toast.show({ type: 'error', text1: 'Lỗi kết nối', text2: 'Không thể đăng ký.' });
    }
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{ flex: 1 }}>
      <ImageBackground source={{ uri: 'https://my.pay2s.vn/assets/media/photos/<EMAIL>' }} style={styles.background}>
        <View style={styles.overlay} />
        <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
          <View style={styles.form}>
            <Image source={{ uri: 'https://docs.pay2s.vn/assets/logo.png' }} style={styles.logo} />
<View style={styles.rowTwoInput}>
  <TextInput
    style={[styles.inputHalf, { marginRight: 5 }]}
    placeholder="Họ"
    placeholderTextColor="#aaa"
    value={lastname}
    onChangeText={setLastname}
  />
  <TextInput
    style={[styles.inputHalf, { marginLeft: 5 }]}
    placeholder="Tên"
    placeholderTextColor="#aaa"
    value={firstname}
    onChangeText={setFirstname}
  />
</View>
           <TextInput
             style={styles.input}
             placeholder="Tên đăng nhập"
             placeholderTextColor="#aaa"
             value={username}
             onChangeText={setUsername}
           />
            <TextInput style={styles.input} placeholder="Mật khẩu" secureTextEntry placeholderTextColor="#aaa" value={password} onChangeText={setPassword} />
            <TextInput style={styles.input} placeholder="Nhập lại mật khẩu" secureTextEntry placeholderTextColor="#aaa" value={repassword} onChangeText={setRepassword} />
            <TextInput style={styles.input} placeholder="Email" placeholderTextColor="#aaa" value={email} onChangeText={setEmail} />
            <TextInput style={styles.input} placeholder="Số điện thoại" placeholderTextColor="#aaa" value={phone} onChangeText={setPhone} keyboardType="phone-pad" />

            <TouchableOpacity style={styles.button} onPress={handleRegister}>
              <Text style={styles.buttonText}>TẠO TÀI KHOẢN</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Text style={styles.backText}>‹ Quay lại đăng nhập</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
rowTwoInput: {
  flexDirection: 'row',
  width: '100%',
  marginBottom: 15, // ⬅️ tạo khoảng cách với ô kế tiếp
},

inputHalf: {
  flex: 1,
  height: 44,
  borderWidth: 1,
  borderColor: '#ccc',
  borderRadius: 6,
  paddingHorizontal: 10,
  backgroundColor: '#fff',
  color: '#333',
  marginBottom: 15
},
  background: { flex: 1 },
  overlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.7)' },
  container: { flexGrow: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  form: {
    backgroundColor: '#fff', borderRadius: 14, padding: 24, alignItems: 'center', width: width * 0.9,
    shadowColor: '#000', shadowOpacity: 0.2, shadowRadius: 10, shadowOffset: { width: 0, height: 3 }, elevation: 5,
  },
  logo: { width: width * 0.4, height: width * 0.4, marginBottom: 10, resizeMode: 'contain' },
  input: {
    width: '100%',
    height: 44,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    color: '#333',
    marginBottom: 15, // ✅ đừng quên dòng này
  },
  rowTwoInput: { flexDirection: 'row', justifyContent: 'space-between', width: '100%' },
  button: {
    backgroundColor: '#308a5a', paddingVertical: 12, borderRadius: 6, width: '100%', alignItems: 'center', marginTop: 5,
  },
  buttonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
  backText: { marginTop: 15, color: '#308a5a', fontWeight: '600' },
});

export default RegisterScreen;