// ✅ TransactionStatsScreen.tsx - FINAL FIXED VERSION
// Đã sửa lỗi RenderError do chuỗi text không bọc trong <Text>

import React, { useEffect, useState, useCallback } from 'react';
import {
  View, Text, StyleSheet, ScrollView, RefreshControl,
  TouchableOpacity, Image, Pressable, Modal, TouchableWithoutFeedback, TextInput
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import moment from 'moment';
import PageLayout from '../components/PageLayout';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useFocusEffect } from '@react-navigation/native';

const TABS = [
  { type: 'ALL', label: '🔄 Tất cả' },
  { type: 'IN', label: '🟢 Tiền vào' },
  { type: 'OUT', label: '🔴 Tiền ra' },
];

const BANKS = ['ACB', 'BIDV', 'MBBank', 'Vietcombank', 'Seabank', 'TPBank', 'OCB'];

const getBankLogo = (shortBankName) => {
  switch (shortBankName.toUpperCase()) {
    case 'MBB': return 'https://my.pay2s.vn/assets/media/banks/mbb.jpg';
    case 'BIDV': return 'https://my.pay2s.vn/assets/media/banks/bidv.jpg';
    case 'ACB': return 'https://my.pay2s.vn/assets/media/banks/acb.jpg';
    case 'MOMO': return 'https://my.pay2s.vn/assets/media/banks/momo.jpg';
    case 'VTB': return 'https://my.pay2s.vn/assets/media/banks/vtb.jpg';
    case 'TPB': return 'https://img.mservice.com.vn/momo_app_v2/img/TPB.png';
    case 'TCB': return 'https://img.mservice.com.vn/momo_app_v2/img/TCB.png';
    case 'VCB': return 'https://my.pay2s.vn/assets/media/banks/vcb.jpg';
    case 'SEAB': return 'https://my.pay2s.vn/assets/media/banks/seab.jpg';
    case 'OCB': return 'https://img.mservice.com.vn/momo_app_v2/img/OCB.png';
    default: return 'https://my.pay2s.vn/assets/media/banks/default.jpg';
  }
};

export default function TransactionStatsScreen() {
  const [transactions, setTransactions] = useState([]);
  const [grouped, setGrouped] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [tabType, setTabType] = useState('ALL');
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalIn, setTotalIn] = useState(0);
  const [totalOut, setTotalOut] = useState(0);
  const [showFilter, setShowFilter] = useState(false);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [selectedBanks, setSelectedBanks] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  const toggleBankSelection = (bank) => {
    setSelectedBanks(prev =>
      prev.includes(bank) ? prev.filter(b => b !== bank) : [...prev, bank]
    );
  };

  const fetchTransactions = useCallback(async () => {
      setRefreshing(true);
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userId = await AsyncStorage.getItem('userId');
      if (!token || !userId) return;

      const res = await axios.post('https://api.pay2s.vn/api/v1/bank', `action=list_transactions&user_id=${userId}`, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`
        }
      });

      if (res.data.status && Array.isArray(res.data.transactions)) {
        let data = res.data.transactions;
        if (tabType !== 'ALL') data = data.filter(tx => tx.type === tabType);
        if (selectedBanks.length > 0) data = data.filter(tx => selectedBanks.includes(tx.shortBankName));
        if (fromDate) data = data.filter(tx => moment(tx.transactionDate).isSameOrAfter(fromDate, 'day'));
        if (toDate) data = data.filter(tx => moment(tx.transactionDate).isSameOrBefore(toDate, 'day'));
        if (searchKeyword.trim() !== '') {
          const keyword = searchKeyword.trim().toLowerCase();
          data = data.filter(tx =>
            tx.description.toLowerCase().includes(keyword) ||
            tx.accountNumber.toLowerCase().includes(keyword)
          );
        }
        const groupedData = {};
        data.forEach(tx => {
          const dateKey = moment(tx.transactionDate).format('DD/MM/YYYY');
          if (!groupedData[dateKey]) groupedData[dateKey] = [];
          groupedData[dateKey].push(tx);
        });

        setGrouped(groupedData);
        calculateStats(data);
      } else {
        setGrouped({});
        calculateStats([]);
      }
    } catch (err) {
      console.error('Transaction error:', err);
    } finally {
      setRefreshing(false);
    }
  }, [tabType, fromDate, toDate, selectedBanks, searchKeyword]);

  const calculateStats = (data) => {
    let total = 0, tin = 0, tout = 0;
    data.forEach(item => {
      const amt = parseFloat(item.amount);
      total += amt;
      if (item.type === 'IN') tin += amt;
      else if (item.type === 'OUT') tout += amt;
    });
    setTotalAmount(total);
    setTotalIn(tin);
    setTotalOut(tout);
  };

  useFocusEffect(useCallback(() => { fetchTransactions(); }, [fetchTransactions]));
  useEffect(() => { fetchTransactions(); }, [fetchTransactions]);

  const isToday = (date) => moment(date, 'DD/MM/YYYY').isSame(moment(), 'day');

  return (
    <PageLayout>
      <TextInput
        placeholder="Tìm kiếm mô tả hoặc STK"
        placeholderTextColor="#aaa"
        value={searchKeyword}
        onChangeText={setSearchKeyword}
        style={[styles.searchBox, { color: '#2c3e50' }]}
      />
<View style={styles.statsRow}>
  <View style={[styles.statBox, { backgroundColor: '#e3f2fd' }]}>
    <Text style={styles.statTitle}>Tổng GD</Text>
     <Text style={[styles.statValue, { color: '#2c3e50' }]}>{`${totalAmount.toLocaleString('vi-VN')} ₫`}</Text>
  </View>
  <View style={[styles.statBox, { backgroundColor: '#e8f5e9' }]}>
    <Text style={[styles.statTitle, { color: '#2ecc71' }]}>Tiền vào</Text>
    <Text style={[styles.statValue, { color: '#2ecc71' }]}>
      {`+${totalIn.toLocaleString('vi-VN')} ₫`}
    </Text>
  </View>
  <View style={[styles.statBox, { backgroundColor: '#fdecea' }]}>
    <Text style={[styles.statTitle, { color: '#e74c3c' }]}>Tiền ra</Text>
    <Text style={[styles.statValue, { color: '#e74c3c' }]}>
      {`-${totalOut.toLocaleString('vi-VN')} ₫`}
    </Text>
  </View>
</View>
      <View style={styles.tabsContainer}>
        {TABS.map(tab => (
          <TouchableOpacity key={tab.type} onPress={() => setTabType(tab.type)} style={[styles.tab, tabType === tab.type && styles.activeTab]}>
            <Text style={[styles.tabText, tabType === tab.type && styles.activeTabText]}>{tab.label}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity onPress={() => setShowFilter(true)} style={styles.filterButton}>
          <Text style={{ color: '#fff', fontSize: 16 }}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scroll} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={fetchTransactions} />}>
        {Object.keys(grouped).length === 0 ? (
          <Text style={styles.noData}>Không có giao dịch</Text>
        ) : (
          Object.keys(grouped).sort((a, b) => moment(b, 'DD/MM/YYYY').diff(moment(a, 'DD/MM/YYYY'))).map(date => (
            <View key={date}>
              <Text style={[styles.dateLabel, isToday(date) && styles.today]}>{isToday(date) ? 'Hôm nay' : date}</Text>
              {grouped[date].map(tx => (
                <Pressable key={tx.transactionID} style={styles.txItem} onPress={() => setSelectedTransaction(tx)}>
                  <View style={styles.avatarContainer}>
                    <Image
                      source={{ uri: getBankLogo(tx.shortBankName) }}
                      style={styles.avatar}
                      resizeMode="contain"
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.bankName}>{tx.bankName}</Text>
                    <Text style={styles.account}>STK: {tx.accountNumber}</Text>
                    <Text style={styles.desc}>{tx.description}</Text>
                  </View>
                  <View style={{ alignItems: 'flex-end' }}>
                    <Text style={[styles.amount, tx.type === 'IN' ? styles.in : styles.out]}>{tx.type === 'IN' ? '+' : '-'}{parseInt(tx.amount).toLocaleString()} ₫</Text>
                    <Text style={styles.time}>{moment(tx.transactionDate).format('HH:mm:ss')}</Text>
                  </View>
                </Pressable>
              ))}
            </View>
          ))
        )}
      </ScrollView>

      <Modal visible={showFilter} transparent animationType="slide">
        <TouchableWithoutFeedback onPress={() => setShowFilter(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.modal}>
                <Text style={styles.modalTitle}>Bộ lọc nâng cao</Text>

                <Text style={styles.label}>Chọn ngân hàng</Text>
                {BANKS.map(bank => (
                  <TouchableOpacity key={bank} onPress={() => toggleBankSelection(bank)}>
                   <Text style={{ padding: 6, fontSize: 15, color: '#2c3e50' }}>
                     <Text>{selectedBanks.includes(bank) ? '✅' : '⬜️'} {bank}</Text>
                   </Text>
                  </TouchableOpacity>
                ))}

                <Text style={styles.label}>Từ ngày</Text>
                <TouchableOpacity onPress={() => setShowDatePicker('from')}>
                  <Text style={styles.dateText}>{fromDate ? moment(fromDate).format('DD/MM/YYYY') : 'Chọn ngày'}</Text>
                </TouchableOpacity>

                <Text style={styles.label}>Đến ngày</Text>
                <TouchableOpacity onPress={() => setShowDatePicker('to')}>
                  <Text style={styles.dateText}>{toDate ? moment(toDate).format('DD/MM/YYYY') : 'Chọn ngày'}</Text>
                </TouchableOpacity>

                {showDatePicker && (
                  <DateTimePicker
                    mode="date"
                    value={showDatePicker === 'from' ? fromDate || new Date() : toDate || new Date()}
                    onChange={(_, selectedDate) => {
                      if (showDatePicker === 'from') setFromDate(selectedDate || null);
                      else setToDate(selectedDate || null);
                      setShowDatePicker(null);
                    }}
                  />
                )}

                <View style={styles.modalActions}>
                  <Pressable onPress={() => { setFromDate(null); setToDate(null); setSelectedBanks([]); }}>
                    <Text style={{ color: '#e74c3c' }}>🗑️ Xóa bộ lọc</Text>
                  </Pressable>
                  <Pressable onPress={() => { setShowFilter(false); fetchTransactions(); }}>
                    <Text style={{ color: '#308a5a', fontWeight: 'bold' }}>Áp dụng</Text>
                  </Pressable>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      <Modal visible={!!selectedTransaction} transparent animationType="fade">
        <TouchableWithoutFeedback onPress={() => setSelectedTransaction(null)}>
          <View style={styles.modalOverlay}>
            <View style={styles.modal}>
              <Text style={styles.modalTitle}>Chi tiết giao dịch</Text>
              {selectedTransaction && (
                <>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label, { color: '#2c3e50' }}>Ngân hàng: </Text>
                   <Text style={{ color: '#2c3e50' }}>{selectedTransaction.bankName}</Text>
                 </Text>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label}>STK: </Text>
                   <Text style={{ color: '#2c3e50' }}>{selectedTransaction.accountNumber}</Text>
                 </Text>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label}>Loại: </Text>
                   <Text style={{ color: '#2c3e50' }}> {selectedTransaction.type}</Text>
                 </Text>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label}>Số tiền: </Text>
                   <Text style={{ color: '#2c3e50' }}>{parseInt(selectedTransaction.amount).toLocaleString('vi-VN')} ₫</Text>
                 </Text>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label}>Nội dung: </Text>
                   <Text style={{ color: '#2c3e50' }}>{selectedTransaction.description}</Text>
                 </Text>

                 <Text style={{ marginTop: 6 }}>
                   <Text style={styles.label}>Thời gian: </Text>
                   <Text style={{ color: '#2c3e50' }}>{moment(selectedTransaction.transactionDate).format('DD/MM/YYYY HH:mm:ss')}</Text>
                 </Text>
                </>
              )}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
    searchBox: {
        backgroundColor: '#fff', margin: 12, padding: 10, borderRadius: 8,
        fontSize: 15, borderColor: '#ccc', borderWidth: 1, color: '#000000'
      },
  scroll: { flex: 1, backgroundColor: '#f2f2f2' },
  noData: { textAlign: 'center', marginTop: 30, color: '#aaa' },
  dateLabel: { fontSize: 16, fontWeight: 'bold', paddingHorizontal: 16, marginTop: 20, marginBottom: 10, color: '#2c3e50' },
  today: { color: '#e67e22' },
  txItem: {
    backgroundColor: '#fff', marginHorizontal: 12, marginBottom: 10,
    padding: 16, borderRadius: 12, flexDirection: 'row', alignItems: 'center',
    shadowColor: '#000', shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1, shadowRadius: 3, elevation: 2
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  bankName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  account: { color: '#555', fontSize: 13, color: '#2c3e50' },
  desc: { color: '#666', marginTop: 2 , color: '#2c3e50'},
  amount: { fontWeight: 'bold', fontSize: 16 },
  in: { color: '#2ecc71' },
  out: { color: '#e74c3c' },
  time: { fontSize: 12, color: '#999', marginTop: 4 },
  tabsContainer: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#fff', paddingHorizontal: 12, paddingVertical: 8 },
  tab: { marginRight: 10, paddingVertical: 6, paddingHorizontal: 12, borderRadius: 20, backgroundColor: '#e0f2f1' },
  activeTab: { backgroundColor: '#308a5a' },
  tabText: { fontWeight: '600', color: '#308a5a' },
  activeTabText: { color: '#fff' },
  filterButton: { marginLeft: 'auto', backgroundColor: '#308a5a', padding: 8, borderRadius: 20 },
  modalOverlay: { flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 },
  modal: { backgroundColor: 'white', borderRadius: 10, padding: 20 },
  modalTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 , color: '#2c3e50'},
  label: { fontSize: 14, marginTop: 12, fontWeight: 'bold', color: '#2c3e50' },
  dateText: { padding: 10, backgroundColor: '#f0f0f0', borderRadius: 6, marginTop: 5 , color: '#2c3e50'},
  modalActions: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 20 },
  statsRow: { flexDirection: 'row', justifyContent: 'space-around', backgroundColor: '#fff', paddingVertical: 10 },
  statBox: { alignItems: 'center', padding: 10, borderRadius: 10 },
  statTitle: { fontSize: 13, fontWeight: 'bold', color: '#555' , color: '#2c3e50'},
  statValue: { fontSize: 16, fontWeight: 'bold', marginTop: 4 }
});