import React, { useState, useEffect } from 'react';
import {
  View, Text, TextInput, StyleSheet, ScrollView, Image,
  TouchableOpacity, Dimensions, ImageBackground, KeyboardAvoidingView,
  Platform, Switch
} from 'react-native';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import socket from '../socket';
import Toast from 'react-native-toast-message';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';

type RootStackParamList = {
  Login: undefined;
  Home: undefined;
};

type LoginScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'Login'>;
  route: RouteProp<RootStackParamList, 'Login'>;
};

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [secure, setSecure] = useState(true);
  const [rememberMe, setRememberMe] = useState(true); // ✅ mặc định true

  useEffect(() => {
    (async () => {
      const savedUsername = await AsyncStorage.getItem('rememberedUsername');
      const savedPassword = await AsyncStorage.getItem('rememberedPassword');
      if (savedUsername) {
        setUsername(savedUsername);
        setPassword(savedPassword);
        setRememberMe(true);
      }
    })();
const checkRemembered = async () => {
    const savedUsername = await AsyncStorage.getItem('rememberedUsername');
    const savedPassword = await AsyncStorage.getItem('rememberedPassword');

    if (savedUsername && savedPassword) {
      setUsername(savedUsername);
      setPassword(savedPassword);
      setRememberMe(true);
      // ✅ Tự động đăng nhập
      handleLogin(savedUsername, savedPassword);
    }
  };

  checkRemembered();
  }, []);

  const handleLogin = async (loginUser?: string, loginPass?: string) => {
    const u = loginUser ?? username;
    const p = loginPass ?? password;

    if (!u || !p) {
      Toast.show({
        type: 'error',
        text1: 'Thiếu thông tin',
        text2: 'Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu.',
      });
      return;
    }

    try {
      const data = `action=login&username=${encodeURIComponent(u)}&password=${encodeURIComponent(p)}`;
      const response = await axios.post('https://api.pay2s.vn/api/v1/auth', data, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });

      if (response.data.status) {
        await AsyncStorage.setItem('userToken', response.data.token);
        await AsyncStorage.setItem('userId', response.data.user_id.toString());

        if (rememberMe) {
          await AsyncStorage.setItem('rememberedUsername', u);
          await AsyncStorage.setItem('rememberedPassword', p);
        }

        socket.connect();
        socket.emit('join', response.data.user_id.toString());

        const fcmToken = await messaging().getToken();
        const fcmPostData = `action=user_fcm_tokens&token=${encodeURIComponent(fcmToken)}&user_id=${encodeURIComponent(response.data.user_id)}`;
        await axios.post('https://api.pay2s.vn/api/v1/user', fcmPostData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${response.data.token}`
          }
        });

        navigation.navigate('Home');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Đăng nhập thất bại',
          text2: response.data.message || 'Sai thông tin đăng nhập',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi kết nối',
        text2: 'Không thể kết nối tới máy chủ.',
      });
    }
  };


  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }}>
      <ImageBackground
        source={{ uri: 'https://my.pay2s.vn/assets/media/photos/<EMAIL>' }}
        style={styles.background}
      >
        <View style={styles.overlay} />
        <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
          <View style={styles.form}>
            <Image source={{ uri: 'https://docs.pay2s.vn/assets/logo.png' }} style={styles.logo} />

            <View style={styles.inputContainer}>
              <FontAwesome name="user" size={18} color="#999" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Tên đăng nhập"
                placeholderTextColor="#aaa"
                value={username}
                onChangeText={setUsername}
              />
            </View>

            <View style={styles.inputContainer}>
              <FontAwesome name="lock" size={18} color="#999" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Mật khẩu"
                placeholderTextColor="#aaa"
                secureTextEntry={secure}
                value={password}
                onChangeText={setPassword}
              />
              <TouchableOpacity onPress={() => setSecure(!secure)}>
                <FontAwesome
                  name={secure ? 'eye-slash' : 'eye'}
                  size={18}
                  color="#999"
                  style={{ paddingHorizontal: 8 }}
                />
              </TouchableOpacity>
            </View>

            {/* ✅ Nhớ tài khoản */}
            <View style={styles.rememberRow}>
              <Text style={{ color: '#333' }}>🔒 Ghi nhớ tài khoản</Text>
              <Switch
                value={rememberMe}
                onValueChange={setRememberMe}
                trackColor={{ false: '#ccc', true: '#308a5a' }}
              />
            </View>

            <TouchableOpacity style={styles.button} onPress={handleLogin}>
              <Text style={styles.buttonText}>ĐĂNG NHẬP</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  background: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  overlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.75)' },
  container: { flexGrow: 1, justifyContent: 'center', alignItems: 'center', padding: 20, width: '100%' },
  form: {
    backgroundColor: '#fff',
    borderRadius: 14,
    padding: 24,
    alignItems: 'center',
    width: width * 0.85,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 3 },
    elevation: 5,
  },
  logo: { width: width * 0.4, height: width * 0.4, marginBottom: 20, resizeMode: 'contain' },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    marginBottom: 15,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  inputIcon: { marginRight: 10 },
  input: { flex: 1, height: 44, color: '#333' },
  rememberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#308a5a',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 6,
    width: '100%',
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
});

export default LoginScreen;
