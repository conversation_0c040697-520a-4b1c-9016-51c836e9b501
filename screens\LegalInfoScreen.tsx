import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import PageLayout from '../components/PageLayout';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { useNavigation } from '@react-navigation/native';

export default function LegalInfoScreen() {
    const navigation = useNavigation();
    const openLink = (url: string) => {
      navigation.navigate('Webview', { url });
    };
  return (
    <PageLayout title="📄 Thông tin pháp lý">
      <ScrollView style={styles.container}>
        <View style={styles.sectionBox}>
          <Text style={styles.sectionTitle}>Ch<PERSON>h sách & <PERSON><PERSON><PERSON></Text>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/open-api-banking#team-sec')}>
            <FontAwesome name="angle-right" size={18} color="#888" style={styles.icon} />
            <Text style={styles.text}>Công bố hợp tác</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/chinh-sach-bao-mat')}>
            <FontAwesome name="angle-right" size={18} color="#888" style={styles.icon} />
            <Text style={styles.text}>Chính sách bảo mật</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/thoa-thuan')}>
            <FontAwesome name="angle-right" size={18} color="#888" style={styles.icon} />
            <Text style={styles.text}>Thỏa thuận sử dụng dịch vụ</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/tiep-nhan-xu-ly')}>
            <FontAwesome name="angle-right" size={18} color="#888" style={styles.icon} />
            <Text style={styles.text}>Tiếp nhận & Xử lý khiếu nại</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
  container: { padding: 20 },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#308a5a',
    marginBottom: 12,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  icon: {
    marginRight: 10,
  },
  text: {
    fontSize: 15,
    color: '#333',
  },
});