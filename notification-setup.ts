import PushNotification from 'react-native-push-notification';

PushNotification.configure({
  onNotification: function (notification) {
    console.log('📨 Notification:', notification);
  },
  requestPermissions: Platform.OS === 'ios',
});

// ✅ Tạo kênh cho Android (chỉ cần tạo 1 lần)
PushNotification.createChannel(
  {
    channelId: 'default-channel-id',
    channelName: 'Thông báo chung',
    importance: 4,
    vibrate: true,
  },
  (created) => console.log(`🔔 Channel created: ${created}`)
);
