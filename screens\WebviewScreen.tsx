import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { useNotifications } from '../contexts/NotificationContext';

type WebviewRouteProp = RouteProp<{ Webview: { url: string; title?: string } }, 'Webview'>;

export default function WebviewScreen() {
     const { notifications, unreadCount, markAsRead } = useNotifications(); // ✅ Gọi đúng chỗ

  const navigation = useNavigation();
  const route = useRoute<WebviewRouteProp>();
  const { url, title } = route.params;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backBtn}>
          <FontAwesome name="arrow-left" size={20} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title || 'Trang Web'}</Text>
      </View>

      {/* Web content */}
      <WebView source={{ uri: url }} style={styles.webview} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    height: 56,
    backgroundColor: '#308a5a',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    elevation: 4,
  },
  backBtn: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  webview: {
    flex: 1,
  },
});
