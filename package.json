{"name": "Pay2SApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-native-picker/picker": "^2.11.0", "@react-native/new-app-screen": "0.80.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "axios": "^1.9.0", "date-fns": "^4.1.0", "moment": "^2.30.1", "react": "19.1.0", "react-native": "0.80.0", "react-native-animatable": "^1.4.0", "react-native-biometrics": "^3.0.1", "react-native-chart-kit": "^6.12.0", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "^2.26.0", "react-native-keep-awake": "^4.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-push-notification": "^8.1.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-sound": "^0.11.2", "react-native-splash-screen": "^3.3.0", "react-native-toast-message": "^2.3.0", "react-native-tts": "^4.1.1", "react-native-vector-icons": "^10.2.0", "react-native-websocket": "^1.0.2", "react-native-webview": "^13.15.0", "rn-fetch-blob": "^0.12.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.8", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-navigation": "^3.4.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-make": "^1.0.1", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}