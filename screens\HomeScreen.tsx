// File: HomeScreen.tsx
import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Alert,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import moment from 'moment';
import PageLayout from '../components/PageLayout';
import { StackNavigationProp } from '@react-navigation/stack';
import { useFocusEffect } from '@react-navigation/native';
import KeepAwake from 'react-native-keep-awake';
import { RootStackParamList } from '../types';
import { useNotifications } from '../contexts/NotificationContext';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;
type Props = {
  navigation: HomeScreenNavigationProp;
};

interface ProfileResponse {
  status: boolean;
  message: {
    firstname: string;
    lastname: string;
    username: string;
    credit: number;
    company_name?: string;
    current_plan?: string;
  };
}

interface InvoiceResponse {
  status: boolean;
  invoices: {
    status: string;
    created_at: string;
  }[];
  message?: string;
}

export default function HomeScreen({ navigation }: Props) {
  KeepAwake.activate();
  const { fetchNotifications } = useNotifications();

  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [currentPlan, setCurrentPlan] = useState('');
  const [balance, setBalance] = useState(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pendingTransactions, setPendingTransactions] = useState(0);
  const [transactionsToday, setTransactionsToday] = useState(0);
  const [transactionsYesterday, setTransactionsYesterday] = useState(0);
  const [transactionsThisMonth, setTransactionsThisMonth] = useState(0);
  const [linkedBanks, setLinkedBanks] = useState<any[]>([]);
  const [typedText, setTypedText] = useState('');
  const [fullText, setFullText] = useState('');
  const [blinkAnim] = useState(new Animated.Value(1));
  const [blogPosts, setBlogPosts] = useState<any[]>([]);
  const [loadingBankIds, setLoadingBankIds] = useState<number[]>([]);
  const screenWidth = Dimensions.get('window').width;
  const getGreetingByTime = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 11) return 'Chào buổi sáng,';
    if (hour >= 11 && hour < 18) return 'Chào buổi chiều,';
    if (hour >= 18 && hour < 22) return 'Chào buổi tối,';
    return 'Chúc bạn ngủ ngon,';
  };

  const getBadgeStyle = (type: string) => {
    switch (type.toLowerCase()) {
      case 'openapi':
        return { backgroundColor: '#f39c12' };
      case 'business':
        return { backgroundColor: '#2ecc71' };
      default:
        return { backgroundColor: '#3498db' };
    }
  };
const fetchBlogPosts = async () => {
  try {
    const res = await axios.get(
      'https://pay2s.vn/blog/wp-json/wp/v2/posts?per_page=10&_embed'
    );
    const data = res.data.map((item: any) => {
      const thumbnail =
        item._embedded?.['wp:featuredmedia']?.[0]?.source_url || null;
      return {
        id: item.id,
        title: item.title.rendered,
        link: item.link,
        image: thumbnail,
        excerpt: item.excerpt.rendered.replace(/<[^>]*>?/gm, ''),
      };
    });
    setBlogPosts(data);
  } catch (err) {
    console.error('Lỗi lấy blog:', err);
  }
};
  const fetchData = useCallback(async () => {
    try {
      const [userToken, userId] = await Promise.all([
        AsyncStorage.getItem('userToken'),
        AsyncStorage.getItem('userId'),
      ]);

      if (!userToken || !userId) throw new Error('Thiếu token hoặc userId');

      const [profileRes, invoiceRes] = await Promise.all([
        axios.post<ProfileResponse>('https://api.pay2s.vn/api/v1/user', `action=get_profile&user_id=${userId}`, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${userToken}`,
          },
        }),
        axios.post<InvoiceResponse>('https://api.pay2s.vn/api/v1/invoices', `action=list_bank_invoices&user_id=${userId}`, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${userToken}`,
          },
        })
      ]);

      if (!profileRes.data.status) throw new Error('Lỗi lấy thông tin tài khoản');
      const user = profileRes.data.message;

      setName(`${user.lastname} ${user.firstname}`);
      setUsername(user.username);
      setCompanyName(user.company_name || '');
      setCurrentPlan(user.current_plan || '');
      setBalance(user.credit);

      if (invoiceRes.data.status) {
        const invoices = invoiceRes.data.invoices;
        setPendingTransactions(invoices.filter(i => i.status === 'pending').length);
        setTransactionsToday(invoices.filter(i => moment(i.created_at).isSame(moment(), 'day')).length);
        setTransactionsYesterday(invoices.filter(i => moment(i.created_at).isSame(moment().subtract(1, 'days'), 'day')).length);
        setTransactionsThisMonth(invoices.filter(i => moment(i.created_at).isSame(moment(), 'month')).length);
      }
    } catch (err: any) {
      console.error('Fetch data error:', err);
      Alert.alert('Lỗi', err?.message || 'Không thể tải dữ liệu');
      await AsyncStorage.multiRemove(['userToken', 'userId']);
      navigation.navigate('Login');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

const toggleBankStatus = async (bankId: number) => {
  try {
    setLoadingBankIds(prev => [...prev, bankId]); // 👉 thêm vào danh sách đang loading

    const [token, userId] = await Promise.all([
      AsyncStorage.getItem('userToken'),
      AsyncStorage.getItem('userId')
    ]);

    if (!token || !userId) return;

    const res = await axios.post(
      'https://api.pay2s.vn/api/v1/bank',
      `action=change_status&user_id=${userId}&bankId=${bankId}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${token}`
        }
      }
    );

    if (res.data.status) {
      setLinkedBanks(prev =>
        prev.map(bank =>
          bank.id === bankId
            ? {
                ...bank,
                status: bank.status === 1 ? 0 : 1,
                statusText: bank.status === 1 ? 'Đã tắt' : 'Đang hoạt động'
              }
            : bank
        )
      );
    } else {
      Alert.alert('Lỗi', res.data.message || 'Không thể thay đổi trạng thái ngân hàng');
    }
  } catch (err) {
    console.error('Lỗi toggleBankStatus:', err);
    Alert.alert('Lỗi', 'Không thể thay đổi trạng thái ngân hàng');
  } finally {
    setLoadingBankIds(prev => prev.filter(id => id !== bankId)); // ✅ xóa khỏi loading
  }
};


  const fetchLinkedBanks = useCallback(async () => {
    try {
      const [userToken, userId] = await Promise.all([
        AsyncStorage.getItem('userToken'),
        AsyncStorage.getItem('userId'),
      ]);
      if (!userToken || !userId) return;

      const res = await axios.post(
        'https://api.pay2s.vn/api/v1/bank',
        `action=bank_account&user_id=${userId}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Bearer ${userToken}`,
          },
        }
      );

      if (res.data.status && Array.isArray(res.data.banks)) {
        setLinkedBanks(res.data.banks);
      }
    } catch (err) {
      console.error('Lỗi khi lấy danh sách ngân hàng:', err);
    }
  }, []);

  useEffect(() => {
    setFullText(getGreetingByTime());
    Animated.loop(
      Animated.sequence([
        Animated.timing(blinkAnim, {
          toValue: 0,
          duration: 700,
          useNativeDriver: true,
        }),
        Animated.timing(blinkAnim, {
          toValue: 1,
          duration: 700,
          useNativeDriver: true,
        })
      ])
    ).start();
  }, []);

  useEffect(() => {
    if (!fullText) return;
    let index = 0;
    let typingInterval: NodeJS.Timeout;
    let restartTimeout: NodeJS.Timeout;

    const startTyping = () => {
      setTypedText('');
      typingInterval = setInterval(() => {
        setTypedText(prev => {
          if (index < fullText.length) {
            const nextChar = fullText.charAt(index);
            index++;
            return prev + nextChar;
          } else {
            clearInterval(typingInterval);
            restartTimeout = setTimeout(() => {
              index = 0;
              startTyping();
            }, 5000);
            return prev;
          }
        });
      }, 100);
    };

    startTyping();
    return () => {
      clearInterval(typingInterval);
      clearTimeout(restartTimeout);
    };
  }, [fullText]);

  useFocusEffect(
    useCallback(() => {
        fetchBlogPosts();
      setLoading(true);
      fetchNotifications();
      fetchData();
      fetchLinkedBanks();
    }, [])
  );

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
    fetchLinkedBanks();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#308a5a" />
        <Text style={{ marginTop: 10 }}>Đang tải dữ liệu...</Text>
      </View>
    );
  }
  return (
    <PageLayout>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.content}>
<View style={styles.userCard}>
  <View style={styles.userCardTop}>
    <View>
     <Text style={styles.welcome}>{typedText}</Text>
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      <Text style={styles.fullname}>{name}</Text>
      <Animated.View
        style={{
          width: 8,
          height: 8,
          borderRadius: 4,
          backgroundColor: '#2ecc71',
          marginLeft: 6,
          marginRight: 4,
          opacity: blinkAnim,
        }}
      />
      <Text style={styles.online}>Online</Text>
    </View>

      {companyName ? (
        <>
          <View style={styles.companyBadge}>
            <Text style={styles.companyText}>{companyName}</Text>
          </View>
          <Text style={styles.planText}>
            Gói sử dụng: <Text style={styles.planName}>{currentPlan}</Text>
          </Text>
        </>
      ) : (
           <>
        <View style={styles.companyBadge}>
                   <Text style={styles.companyText}>{username}</Text>
                 </View>
                 <Text style={styles.planText}>
                   Gói sử dụng: <Text style={styles.planName}>{currentPlan}</Text>
                 </Text>
                  </>
      )}
    </View>

    <Image
      source={{ uri: 'https://my.pay2s.vn/assets/media/avatars/waving-hand.svg' }}
      style={styles.userAvatar}
    />
  </View>
</View>

       <ScrollView
         horizontal
         showsHorizontalScrollIndicator={false}
         style={{ marginTop: 10 }}
       >
         <View style={styles.statCard}>
           <FontAwesome name="clock-o" size={28} color="#f39c12" />
           <Text style={styles.statNumber}>{pendingTransactions}</Text>
           <Text style={styles.statLabel}>Chờ thanh toán</Text>
         </View>

         <View style={styles.statCard}>
           <FontAwesome name="calendar" size={28} color="#3498db" />
           <Text style={styles.statNumber}>{transactionsToday}</Text>
           <Text style={styles.statLabel}>Giao dịch hôm nay</Text>
         </View>

         <View style={styles.statCard}>
           <FontAwesome name="calendar-check-o" size={28} color="#2ecc71" />
           <Text style={styles.statNumber}>{transactionsYesterday}</Text>
           <Text style={styles.statLabel}>Hôm qua</Text>
         </View>

         <View style={styles.statCard}>
           <FontAwesome name="line-chart" size={28} color="#e74c3c" />
           <Text style={styles.statNumber}>{transactionsThisMonth}</Text>
           <Text style={styles.statLabel}>Tháng này</Text>
         </View>
       </ScrollView>

<View style={styles.section}>
<View style={styles.sectionDivider}>
  <View style={styles.line} />
  <FontAwesome name="credit-card" size={16} color="#308a5a" />
  <Text style={styles.dividerText}> Tài khoản ngân hàng đã liên kết </Text>
  <View style={styles.line} />
</View>
  {linkedBanks.length === 0 ? (
    <Text style={{ padding: 10, color: '#999' }}>Chưa có tài khoản nào</Text>
  ) : (
<ScrollView
  horizontal
  showsHorizontalScrollIndicator={false}
  contentContainerStyle={{ paddingHorizontal: 16 }}
  snapToInterval={screenWidth - 80 + 12} // width của card + marginRight
  decelerationRate="fast"
  snapToAlignment="start"
 // style={{ marginTop: 10 }}
 // contentContainerStyle={{ paddingHorizontal: 10 }}
>
  {linkedBanks.map((bank, idx) => (
    <View key={idx} style={{
                            width: screenWidth - 80, // Giảm width để có chỗ cho nút toggle
                            marginRight: 12,
                            borderRadius: 12,
                            backgroundColor: '#fff',
                            padding: 16,
                            position: 'relative',
                          }}>
      <TouchableOpacity
        onPress={() => toggleBankStatus(bank.id)}
        disabled={loadingBankIds.includes(bank.id)}
        style={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 10,
          padding: 4,
        }}
      >
        {loadingBankIds.includes(bank.id) ? (
          <ActivityIndicator size="small" color="#308a5a" />
        ) : (
          <FontAwesome
            name={bank.status == 1 ? 'toggle-on' : 'toggle-off'}
            size={28}
            color={bank.status == 1 ? '#2ecc71' : '#ccc'}
          />
        )}
      </TouchableOpacity>
      <View style={styles.bankCardTop}>
        <Image
          source={{ uri: `https://my.pay2s.vn/assets/media/banks/${bank.shortBankName.toLowerCase()}.jpg` }}
          style={styles.bankCardLogo}
        />
      </View>

      <Text style={styles.bankCardName}>{bank.bankName}</Text>

      <View style={styles.detailRow}>
        <FontAwesome name="bank" size={14} color="#888" style={styles.detailIcon} />
        <Text style={styles.detailLabel}>Số tài khoản:</Text>
        <Text style={styles.detailValue} numberOfLines={1} ellipsizeMode="middle">{bank.accountNumber}</Text>
      </View>

      <View style={styles.detailRow}>
        <FontAwesome name="tag" size={14} color="#888" style={styles.detailIcon} />
        <Text style={styles.detailLabel}>Loại tài khoản:</Text>
        <View style={[styles.badge, getBadgeStyle(bank.type)]}>
          <Text style={styles.badgeText}>
            {bank.type === 'openapi'
              ? 'OpenAPI'
              : bank.type === 'business'
              ? 'Doanh nghiệp'
              : 'Cá nhân'}
          </Text>
        </View>
      </View>

      <View style={styles.detailRow}>
        <FontAwesome name="exchange" size={14} color="#888" style={styles.detailIcon} />
        <Text style={styles.detailLabel}>Số lượng giao dịch:</Text>
        <Text style={styles.detailValue} numberOfLines={1}> {bank.transaction_count} giao dịch</Text>
      </View>

      <View style={styles.detailRow}>
        <FontAwesome name="money" size={14} color="#888" style={styles.detailIcon} />
        <Text style={styles.detailLabel}>Tổng tiền:</Text>
        <Text style={styles.detailValue} numberOfLines={1}>
          {parseInt(bank.total_amount).toLocaleString()} ₫
        </Text>
      </View>

      <Text
        style={[
          styles.bankCardStatus,
          { color: bank.status == 1 ? '#2ecc71' : '#e74c3c' },
        ]}
      >
        ● {bank.statusText}
      </Text>
    </View>
  ))}
</ScrollView>

  )}
</View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {blogPosts.map((post) => (
          <TouchableOpacity
            key={post.id}
            style={styles.blogCard}
            onPress={() => {
                console.log('Đã bấm blog:', post.link);
                navigation.navigate('Webview', { url: post.link });
              }}
          >
            {post.image && (
              <Image
                source={{ uri: post.image }}
                style={{ width: 260, height: 140, borderRadius: 8 }}
              />
            )}
            <Text style={styles.blogTitle} numberOfLines={2}>{post.title}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
        </View>
      </ScrollView>
    </PageLayout>
  );
}

const styles = StyleSheet.create({
blogCard: {
  width: 260,
  marginRight: 12,
  backgroundColor: '#fff',
  borderRadius: 10,
  overflow: 'hidden',
  shadowColor: '#000',
  shadowOpacity: 0.1,
  shadowOffset: { width: 0, height: 2 },
  shadowRadius: 4,
  elevation: 3,
},
blogTitle: {
  padding: 8,
  fontSize: 15,
  fontWeight: '600',
  color: '#2c3e50',
},
    blogExcerpt: {
      color: '#555',
      fontSize: 14,
      lineHeight: 20,
    },
    badge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 8,
    },
    badgeText: {
      color: '#fff',
      fontSize: 12,
      fontWeight: 'bold',
    },
userCard: {
  backgroundColor: '#ffffff',
  padding: 20,
  borderRadius: 16,
  marginBottom: 20,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 3 },
  shadowOpacity: 0.05,
  shadowRadius: 6,
  elevation: 3,
  borderLeftWidth: 6,
  borderLeftColor: '#308a5a',
},

userCardTop: {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
},

welcome: {
  fontSize: 14,
  color: '#666',
  marginBottom: 4,
},

fullname: {
  fontSize: 18,
  fontWeight: 'bold',
  color: '#308a5a',
},

online: {
  fontSize: 13,
  color: '#2ecc71',
  fontWeight: '600',
  marginLeft: 4,
},

username: {
  fontSize: 13,
  color: '#555',
  marginTop: 4,
},

userAvatar: {
  width: 38,
  height: 38,
  resizeMode: 'contain',
},

companyBadge: {
  backgroundColor: '#ffd700',
  alignSelf: 'flex-start',
  paddingVertical: 2,
  paddingHorizontal: 8,
  borderRadius: 999,
  marginTop: 8,
  marginBottom: 4,
},

companyText: {
  fontSize: 12,
  color: '#000',
  fontWeight: 'bold',
},

planText: {
  fontSize: 13,
  color: '#666',
},

planName: {
  color: '#308a5a',
  fontWeight: 'bold',
},
   sectionDivider: {
     flexDirection: 'row',
     alignItems: 'center',
     marginVertical: 10,
     justifyContent: 'center',
   },
   line: {
     flex: 1,
     height: 1,
     backgroundColor: '#ddd',
     marginHorizontal: 5,
   },
   dividerText: {
     fontSize: 14,
     fontWeight: 'bold',
     color: '#308a5a',
     paddingHorizontal: 4,
   },
    detailRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
      flex: 1,
    },
    detailIcon: {
      width: 18,
      flexShrink: 0,
    },
    detailLabel: {
      fontSize: 13,
      color: '#888',
      marginLeft: 4,
      minWidth: 80,
      flexShrink: 0,
    },
    detailValue: {
      fontSize: 13,
      color: '#2c3e50',
      fontWeight: '500',
      flex: 1,
      flexWrap: 'wrap',
    },
    statusToggleBtn: {
      marginTop: 8,
      alignSelf: 'flex-start',
      paddingHorizontal: 14,
      paddingVertical: 6,
      borderRadius: 6,
    },
    statusToggleText: {
      color: '#fff',
      fontWeight: 'bold',
      fontSize: 13,
    },
  bankCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 0,
    marginRight: 15,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 3,
    elevation: 1,
  },
  bankCardTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  bankCardLogo: {
    width: 60,
    height: 40,
    resizeMode: 'contain',
  },
  bankCardName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  bankCardInfo: {
    fontSize: 13,
    color: '#555',
    marginBottom: 2,
  },
  bankCardStatus: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 6,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
      flex: 1,
      backgroundColor: '#f8f8f8',
    },
 loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 20,
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    width: 20,
    height: 20,
    marginLeft: 5,
    resizeMode: 'contain',
  },
  welcomeText: {
    fontSize: 20,
  },
  name: {
    color: '#308a5a',
    fontWeight: 'bold',
  },
  balanceText: {
    fontSize: 16,
    color: '#555',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  box: {
    width: '48%',
    paddingVertical: 18,
    paddingHorizontal: 10,
    borderRadius: 16,
    marginBottom: 15,
    alignItems: 'center',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  boxText: {
    fontSize: 24,
    color: '#ffffff',
    fontWeight: 'bold',
    marginTop: 10,
  },
  boxLabel: {
    fontSize: 14,
    color: '#ffffff',
    marginTop: 5,
  },
  section: {
    marginVertical: 20,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#ffffff',
  },
sectionTitle: {
  fontSize: 15,
  fontWeight: 'bold',
  backgroundColor: '#308a5a',
  color: '#ffffff',
  paddingVertical: 10,
  paddingHorizontal: 15,
  borderTopLeftRadius: 10,
  borderTopRightRadius: 10,
  textAlign: 'center',
},
  banksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  bankItem: {
    width: '30%',
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  bankLogo: {
    width: '80%',
    height: '80%',
    resizeMode: 'contain',
  },
  statCard: {
    width: 150,
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 15,
    marginRight: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4
  },

  statNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 10,
  },

  statLabel: {
    fontSize: 13,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 5,
  }
});
