import React, { useEffect, useState } from 'react';
import {
  View, Text, StyleSheet, TouchableOpacity, Image, ScrollView,
  Dimensions, TouchableWithoutFeedback, Platform, StatusBar
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import RenderHtml from 'react-native-render-html';
import { useNavigation } from '@react-navigation/native';
import { useNotifications } from '../contexts/NotificationContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import socket from '../socket';

interface NotificationItem {
  id: number;
  title: string;
  content: string;
  is_read: number;
}

const NotificationDropdown = ({ notifications, onSelect, positionOffset = 90 }: {
  notifications: NotificationItem[],
  onSelect: (id: number) => void,
  positionOffset?: number
}) => {
  const navigation = useNavigation();
  return (
    <View style={[styles.notificationsDropdown, { top: positionOffset }]}>
      <Text style={styles.dropdownTitle}>THÔNG BÁO</Text>
      <ScrollView contentContainerStyle={styles.notificationsContent}>
        {notifications.length === 0 ? (
          <Text style={styles.noNotifications}>Không có thông báo nào</Text>
        ) : (
          notifications.map(notification => (
            <TouchableOpacity
              key={notification.id}
              onPress={() => {
                onSelect(notification.id);
                navigation.navigate('Notifications');
              }}>
              <View style={[styles.notificationItem, notification.is_read === 0 && styles.unreadNotification]}>
                <Text style={styles.notificationTitle} numberOfLines={1}>{notification.title}</Text>
                <RenderHtml
                  contentWidth={Dimensions.get('window').width - 60}
                  source={{ html: notification.content }}
                  tagsStyles={{
                        body: { color: '#2c3e50', fontSize: 14, lineHeight: 22 },
                        p: { color: '#2c3e50' },
                        span: { color: '#2c3e50' },
                      }}
                />
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
      <TouchableOpacity onPress={() => navigation.navigate('Notifications')}>
        <Text style={styles.viewAllText}>Xem tất cả</Text>
      </TouchableOpacity>
    </View>
  );
};

export default function Header() {
  const { notifications, unreadCount, markAsRead, fetchNotifications } = useNotifications();
  const navigation = useNavigation();
  const [showNotifications, setShowNotifications] = useState(false);
  const insets = useSafeAreaInsets();

  useEffect(() => {
      console.log('📦 notifications:', notifications.length);
        console.log('📬 unreadCount:', unreadCount);
    let isMounted = true;
    fetchNotifications();

    AsyncStorage.getItem('userId').then(userId => {
      if (userId) {
        socket.emit('register', userId);
      }
    });

    const onNewTransaction = (data: NotificationItem) => {
      if (!isMounted) return;
      fetchNotifications();
    };

    socket.on('transaction:new', onNewTransaction);

    return () => {
      isMounted = false;
      socket.off('transaction:new', onNewTransaction);
    };
  }, []);

  return (
    <TouchableWithoutFeedback onPress={() => setShowNotifications(false)}>
      <View style={styles.headerContainer}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <Image
            source={{ uri: 'https://my.pay2s.vn/assets/media/logos/pay2s-logo_color_bg_2.png' }}
            style={styles.logo}
          />
          <TouchableOpacity style={styles.notificationButton} onPress={() => setShowNotifications(prev => !prev)}>
            <FontAwesome name="bell" size={24} color="#ffffff" />
{unreadCount > 0 && (
  <View style={styles.notificationBadge}>
    <Text style={styles.notificationBadgeText}>
      {unreadCount > 99 ? '99+' : unreadCount}
    </Text>
  </View>
)}
          </TouchableOpacity>
        </View>

        {showNotifications && (
          <NotificationDropdown
            notifications={notifications}
            onSelect={(id) => {
              markAsRead(id);
              setShowNotifications(false);
            }}
            positionOffset={insets.top + 65}
          />
        )}
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  headerContainer: { zIndex: 1000 },
  header: {
    backgroundColor: '#308a5a',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 10,
    marginTop: 0,
  },
  logo: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
  },
  notificationButton: {
    padding: 10,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#ff0000',
    borderRadius: 10,
    paddingHorizontal: 5,
    paddingVertical: 1,
    minWidth: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  notificationsDropdown: {
    position: 'absolute',
    right: 10,
    width: 280,
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 10,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 2 },
    elevation: 5,
    marginRight: 10,
    zIndex: 1000,
    maxHeight: 400,
  },
  dropdownTitle: { fontWeight: 'bold', fontSize: 16, marginBottom: 10 },
  notificationsContent: { paddingBottom: 10 },
  noNotifications: { textAlign: 'center', color: '#888', marginBottom: 10 },
  notificationItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingBottom: 10,
    marginBottom: 10,
    paddingHorizontal: 4,

  },
  unreadNotification: {
    backgroundColor: '#e6f7f0',
    borderLeftWidth: 3,
    borderLeftColor: '#308a5a',
    borderRadius: 4,
  },
  notificationTitle: { fontWeight: 'bold', fontSize: 14, color: '#2c3e50' },
  viewAllText: {
    color: '#308a5a',
    textAlign: 'center',
    marginTop: 10,
    fontWeight: 'bold',
  },
});